use anyhow::Result;
use clap::Args;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, error, info};

use crate::Runtime;

#[derive(Debug, Args)]
pub struct ElevatedHelper {
    /// Named pipe identifier for communication with main process
    #[clap(long)]
    pub pipe_name: String,
}

/// Command request sent from main process to elevated helper
#[derive(Debug, Serialize, Deserialize)]
pub struct CommandRequest {
    pub id: String,
    pub command: String,
    pub arguments: Vec<String>,
    pub working_dir: Option<String>,
    pub environment: HashMap<String, String>,
}

/// Command response sent from elevated helper back to main process
#[derive(Debug, Serialize, Deserialize)]
pub struct CommandResponse {
    pub id: String,
    pub success: bool,
    pub exit_code: i32,
    pub stdout: String,
    pub stderr: String,
    pub error: Option<String>,
}

impl ElevatedHelper {
    pub fn execute(&self, _runtime: &Runtime) -> Result<()> {
        info!("Starting elevated helper process with pipe: {}", self.pipe_name);

        // Verify we're running with elevated privileges
        if !privilege::user::privileged() {
            return Err(anyhow::anyhow!("Elevated helper must run with administrative privileges"));
        }

        self.run_helper_loop()
    }

    fn run_helper_loop(&self) -> Result<()> {
        use interprocess::local_socket::{ListenerOptions, GenericNamespaced, ToNsName};
        use interprocess::local_socket::traits::ListenerExt;

        debug!("Creating local socket listener for pipe: {}", self.pipe_name);

        // Create a local socket listener using ListenerOptions
        let name = self.pipe_name.clone().to_ns_name::<GenericNamespaced>()
            .map_err(|e| anyhow::anyhow!("Failed to create namespace name: {}", e))?;

        let listener = ListenerOptions::new()
            .name(name)
            .create_sync()
            .map_err(|e| anyhow::anyhow!("Failed to bind to pipe {}: {}", self.pipe_name, e))?;

        info!("Elevated helper listening on pipe: {}", self.pipe_name);

        // Accept connections from the main process
        for stream in listener.incoming() {
            match stream {
                Ok(stream) => {
                    debug!("Accepted connection from main process");
                    if let Err(e) = self.handle_connection(stream) {
                        error!("Error handling connection: {}", e);
                    }
                }
                Err(e) => {
                    error!("Error accepting connection: {}", e);
                    break;
                }
            }
        }

        info!("Elevated helper shutting down");
        Ok(())
    }

    fn handle_connection(&self, stream: interprocess::local_socket::Stream) -> Result<()> {
        use std::io::{BufRead, BufReader, Write};
        use std::sync::{Arc, Mutex};
        use std::thread;

        let stream = Arc::new(Mutex::new(stream));
        let reader_stream = Arc::clone(&stream);
        let writer_stream = Arc::clone(&stream);

        // Create a channel for sending responses back
        let (response_tx, response_rx) = std::sync::mpsc::channel::<CommandResponse>();

        // Spawn a thread to handle writing responses back to main process
        let writer_thread = {
            let writer_stream = Arc::clone(&writer_stream);
            thread::spawn(move || {
                while let Ok(response) = response_rx.recv() {
                    if let Ok(mut stream) = writer_stream.lock() {
                        let response_json = serde_json::to_string(&response).unwrap();
                        if let Err(e) = writeln!(stream, "{}", response_json) {
                            error!("Failed to write response: {}", e);
                            break;
                        }
                        if let Err(e) = stream.flush() {
                            error!("Failed to flush response: {}", e);
                            break;
                        }
                    }
                }
            })
        };

        // Read commands from the main process
        {
            let stream = reader_stream.lock().unwrap();
            let reader = BufReader::new(&*stream);

            for line in reader.lines() {
                match line {
                    Ok(line) => {
                        if line.trim().is_empty() {
                            continue;
                        }

                        debug!("Received command request: {}", line);

                        match serde_json::from_str::<CommandRequest>(&line) {
                            Ok(request) => {
                                let response_tx = response_tx.clone();
                                // Spawn a thread to handle each command asynchronously
                                thread::spawn(move || {
                                    let response = Self::execute_command(request);
                                    if let Err(e) = response_tx.send(response) {
                                        error!("Failed to send response: {}", e);
                                    }
                                });
                            }
                            Err(e) => {
                                error!("Failed to parse command request: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        error!("Error reading from pipe: {}", e);
                        break;
                    }
                }
            }
        }

        // Wait for writer thread to finish
        drop(response_tx); // Close the channel
        if let Err(e) = writer_thread.join() {
            error!("Writer thread panicked: {:?}", e);
        }

        Ok(())
    }

    fn execute_command(request: CommandRequest) -> CommandResponse {
        debug!("Executing privileged command: {} {:?}", request.command, request.arguments);

        let mut cmd = std::process::Command::new(&request.command);
        cmd.args(&request.arguments);

        if let Some(working_dir) = &request.working_dir {
            cmd.current_dir(working_dir);
        }

        for (key, value) in &request.environment {
            cmd.env(key, value);
        }

        match cmd.output() {
            Ok(output) => {
                let success = output.status.success();
                let exit_code = output.status.code().unwrap_or(-1);
                let stdout = String::from_utf8_lossy(&output.stdout).to_string();
                let stderr = String::from_utf8_lossy(&output.stderr).to_string();

                debug!("Command {} completed with exit code: {}", request.command, exit_code);

                CommandResponse {
                    id: request.id,
                    success,
                    exit_code,
                    stdout,
                    stderr,
                    error: None,
                }
            }
            Err(e) => {
                error!("Failed to execute command {}: {}", request.command, e);
                CommandResponse {
                    id: request.id,
                    success: false,
                    exit_code: -1,
                    stdout: String::new(),
                    stderr: String::new(),
                    error: Some(e.to_string()),
                }
            }
        }
    }
}
