use crate::commands::ComtryaCommand;
use crate::config::{Commands, GlobalArgs};

use std::io;
use std::path::PathBuf;

use comtrya_lib::contexts::build_contexts;
use comtrya_lib::contexts::Contexts;
use comtrya_lib::manifests;

use clap::Parser;
use tracing::{debug, error, Level};

#[allow(unused_imports)]
use tracing_subscriber::{fmt::writer::MakeWriterExt, layer::SubscriberExt, FmtSubscriber};

mod commands;
mod config;
use config::Config;

#[derive(Debug)]
pub struct Runtime {
    pub(crate) args: GlobalArgs,
    pub(crate) config: Config,
    pub(crate) contexts: Contexts,
}

impl Runtime {
    /// Checks if any actions in any manifests require elevated privileges
    pub fn requires_privileges(&self) -> bool {
        use comtrya_lib::manifests::load;
        use tracing::debug;

        // Get the manifest path using the same logic as Apply command
        let Ok(manifest_path) = self.get_manifest_path() else {
            debug!("Failed to get manifest path, assuming no privileges needed");
            return false;
        };
        

        debug!("Checking for privilege requirements in manifests at: {:?}", manifest_path);

        // Load manifests
        let manifests = load(manifest_path, &self.contexts);

        // Check each manifest and its actions
        for (name, manifest) in manifests.iter() {
            debug!("Checking manifest: {}", name);

            for action in manifest.actions.iter() {
                let action = action.inner_ref();

                // Plan the action to get the steps
                match action.plan(manifest, &self.contexts) {
                    Ok(steps) => {
                        for step in steps {
                            // Check if any atom requires privileges
                            if self.step_requires_privileges(&step) {
                                debug!("Found privileged step in manifest '{}': {}", name, step.atom);
                                return true;
                            }
                        }
                    }
                    Err(err) => {
                        debug!("Failed to plan action in manifest '{}': {:?}", name, err);
                        // Continue checking other actions
                    }
                }
            }
        }

        debug!("No privileged actions found");
        false
    }

    /// Helper method to get manifest path (similar to Apply::manifest_path)
    fn get_manifest_path(&self) -> anyhow::Result<PathBuf> {
        use std::path::PathBuf;

        if let Some(manifest_directory) = &self.args.manifest_directory {
            Ok(PathBuf::from(manifest_directory))
        } else if !self.config.manifest_paths.is_empty() {
            Ok(PathBuf::from(&self.config.manifest_paths[0]))
        } else {
            Ok(PathBuf::from("./"))
        }
    }

    /// Helper method to check if a step requires privileges
    fn step_requires_privileges(&self, step: &comtrya_lib::steps::Step) -> bool {
        use comtrya_lib::atoms::command::Exec;
        use std::any::Any;

        // Check if the atom is an Exec atom and if it's privileged
        if let Some(exec) = step.atom.as_any().downcast_ref::<Exec>() {
            return exec.privileged;
        }

        // For other atom types, we assume they don't require privileges
        // This can be extended in the future if other atoms support privilege escalation
        false
    }
}

/// Simple privilege detection functions
fn is_elevated() -> bool {
    #[cfg(target_os = "windows")]
    {
        use windows_sys::Win32::Security::Authorization::IsUserAnAdmin;
        unsafe { IsUserAnAdmin() != 0 }
    }

    #[cfg(target_family = "unix")]
    {
        unsafe { libc::geteuid() == 0 }
    }

    #[cfg(not(any(target_os = "windows", target_family = "unix")))]
    {
        false
    }
}

fn get_current_user() -> Option<String> {
    #[cfg(target_os = "windows")]
    {
        std::env::var("USERNAME").ok()
    }

    #[cfg(target_family = "unix")]
    {
        Some(whoami::username())
    }

    #[cfg(not(any(target_os = "windows", target_family = "unix")))]
    {
        None
    }
}

fn store_original_user() {
    if let Some(user) = get_current_user() {
        std::env::set_var("COMTRYA_ORIGINAL_USER", &user);
        debug!("Stored original user: {}", user);
    }
}

fn reexecute_with_privileges() -> anyhow::Result<()> {
    use anyhow::anyhow;
    use std::process::Command;

    let current_exe = std::env::current_exe()?;
    let args: Vec<String> = std::env::args().collect();

    // Store original user for later use
    store_original_user();

    debug!("Re-executing with elevated privileges: {:?} {:?}", current_exe, &args[1..]);

    #[cfg(target_os = "windows")]
    {
        use std::ffi::OsStr;
        use std::os::windows::ffi::OsStrExt;
        use windows_sys::Win32::UI::Shell::ShellExecuteW;

        let program = current_exe.to_string_lossy().to_string();
        let args_str = args[1..].join(" ");

        // Convert to wide strings for Windows API
        let wide_string = |s: &str| -> Vec<u16> {
            OsStr::new(s).encode_wide().chain(std::iter::once(0)).collect()
        };

        let program_w = wide_string(&program);
        let args_w = wide_string(&args_str);
        let operation_w = wide_string("runas");

        unsafe {
            let result = ShellExecuteW(
                std::ptr::null_mut(),
                operation_w.as_ptr(),
                program_w.as_ptr(),
                args_w.as_ptr(),
                std::ptr::null(),
                1, // SW_NORMAL
            );

            if result as i32 <= 32 {
                return Err(anyhow!("Failed to elevate privileges on Windows"));
            }
        }

        // Exit the current process
        std::process::exit(0);
    }

    #[cfg(target_family = "unix")]
    {
        // Try different privilege escalation tools in order of preference
        let tools = ["sudo", "doas", "run0"];

        for tool in &tools {
            if comtrya_lib::utilities::get_binary_path(tool).is_ok() {
                debug!("Using privilege escalation tool: {}", tool);

                let status = Command::new(tool)
                    .arg(&current_exe)
                    .args(&args[1..])
                    .status()?;

                std::process::exit(status.code().unwrap_or(1));
            }
        }

        return Err(anyhow!("No privilege escalation tool found (sudo, doas, run0)"));
    }

    #[cfg(not(any(target_os = "windows", target_family = "unix")))]
    {
        Err(anyhow!("Privilege escalation not supported on this platform"))
    }
}

pub(crate) fn execute(runtime: Runtime) -> anyhow::Result<()> {
    match &runtime.args.command {
        Commands::Apply(apply) => apply.execute(&runtime),
        Commands::Status(apply) => apply.status(&runtime),
        Commands::Version(version) => version.execute(&runtime),
        Commands::Contexts(contexts) => contexts.execute(&runtime),
        Commands::GenCompletions(gen_completions) => gen_completions.execute(&runtime),
    }
}

fn configure_tracing(args: &GlobalArgs) {
    let stdout_writer = match args.verbose {
        0 => io::stdout.with_max_level(tracing::Level::INFO),
        1 => io::stdout.with_max_level(tracing::Level::DEBUG),
        _ => io::stdout.with_max_level(tracing::Level::TRACE),
    };

    let builder = FmtSubscriber::builder()
        .with_max_level(Level::TRACE)
        .with_ansi(!args.no_color)
        .with_target(false)
        .with_writer(stdout_writer)
        .without_time();

    #[cfg(target_os = "linux")]
    if let Ok(layer) = tracing_journald::layer() {
        tracing::subscriber::set_global_default(builder.finish().with(layer))
            .expect("Unable to set a global subscriber");
        return;
    }

    tracing::subscriber::set_global_default(builder.finish())
        .expect("Unable to set a global subscriber");
}

fn main() -> anyhow::Result<()> {
    let args = GlobalArgs::parse();
    configure_tracing(&args);

    let config = match config::load_config(&args) {
        Ok(config) => config,
        Err(error) => {
            error!("{}", error.to_string());
            panic!();
        }
    };

    if !config.disable_update_check {
        check_for_updates(args.no_color);
    }

    // Run Context Providers
    let contexts = build_contexts(&config);
    let runtime = Runtime {
        args,
        config,
        contexts,
    };

    // Check if we need elevated privileges and re-execute if necessary
    if runtime.requires_privileges() && !is_elevated() {
        debug!("Privileges required but not elevated, re-executing with privileges");
        return reexecute_with_privileges();
    }

    execute(runtime)?;

    Ok(())
}

fn check_for_updates(no_color: bool) {
    use colored::*;
    use update_informer::{registry, Check};

    if no_color {
        control::set_override(false);
    }

    let pkg_name = env!("CARGO_PKG_NAME");
    let pkg_version = env!("CARGO_PKG_VERSION");
    let informer = update_informer::new(registry::Crates, pkg_name, pkg_version);

    if let Some(new_version) = informer.check_version().ok().flatten() {
        let msg = format!(
            "A new version of {pkg_name} is available: v{pkg_version} -> {new_version}",
            pkg_name = pkg_name.italic().cyan(),
            new_version = new_version.to_string().green()
        );

        let release_url =
            format!("https://github.com/{pkg_name}/{pkg_name}/releases/tag/{new_version}").blue();
        let changelog = format!("Changelog: {release_url}",);

        let cmd = format!(
            "Run to update: {cmd}",
            cmd = "curl -fsSL https://get.comtrya.dev | sh".green()
        );

        println!("\n{msg}\n{changelog}\n{cmd}");
    }
}
