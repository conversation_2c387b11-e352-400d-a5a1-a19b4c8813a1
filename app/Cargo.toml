[package]
name = "comtrya"
version = "0.9.2"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MIT"
description = "A tool to simplify reprovisioning a fresh OS. Installs packages and manages dotfiles."

[dependencies]
anyhow = "1.0"
clap = { version = "4.5.20", features = ["derive"] }
clap_complete = "4.5.36"
colored = "2.1"
comfy-table = "7"
comtrya-lib = { path = "../lib", version = "0.9.2" }
petgraph = "0.6"
rhai = { version = "1.19", features = ["serde"] }
strip-ansi-escapes = "0.2"
tracing = "0.1"
tracing-journald = "0.3.0"
tracing-subscriber = "0.3"
update-informer = "1.1"
dirs-next = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_yml = "0"
tealr = { version = "0.10.0", features = [
    "mlua",
    "mlua_lua54",
    "tealr_derive",
    "mlua_userdata-wrappers",
    "mlua_serialize",
    "mlua_macros",
    "mlua_vendored",
] }
walkdir = "2.5.0"
gix = { version = "0.70.0", features = ["blocking-network-client", "blocking-http-transport-reqwest-rust-tls", "status"] }
privilege = "0.3.0"

[dev-dependencies]
assert_cmd = "2.0"
predicates = "3.1"
tempfile = "3.13"
